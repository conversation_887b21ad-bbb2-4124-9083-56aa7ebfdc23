package com.callerrep;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;

import java.util.List;

/**
 * React Native module for controlling background service functionality
 */
public class BackgroundServiceModule extends ReactContextBaseJavaModule {
    private static final String TAG = "BackgroundServiceModule";
    private final ReactApplicationContext reactContext;
    
    public BackgroundServiceModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }
    
    @Override
    public String getName() {
        return "BackgroundService";
    }
    
    /**
     * Start the background call detection service
     */
    @ReactMethod
    public void startBackgroundService(Promise promise) {
        try {
            CallDetectionService.startService(reactContext);
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("message", "Background service started successfully");
            
            promise.resolve(result);
            Log.d(TAG, "✅ Background service started via React Native");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to start background service", e);
            promise.reject("START_SERVICE_ERROR", "Failed to start background service: " + e.getMessage());
        }
    }
    
    /**
     * Stop the background call detection service
     */
    @ReactMethod
    public void stopBackgroundService(Promise promise) {
        try {
            CallDetectionService.stopService(reactContext);
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("message", "Background service stopped successfully");
            
            promise.resolve(result);
            Log.d(TAG, "⏹️ Background service stopped via React Native");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to stop background service", e);
            promise.reject("STOP_SERVICE_ERROR", "Failed to stop background service: " + e.getMessage());
        }
    }
    
    /**
     * Check if the background service is currently running
     */
    @ReactMethod
    public void isServiceRunning(Promise promise) {
        try {
            boolean isRunning = isCallDetectionServiceRunning();
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("isRunning", isRunning);
            result.putString("status", isRunning ? "running" : "stopped");
            
            promise.resolve(result);
            Log.d(TAG, "📊 Service status checked: " + (isRunning ? "running" : "stopped"));
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to check service status", e);
            promise.reject("CHECK_SERVICE_ERROR", "Failed to check service status: " + e.getMessage());
        }
    }
    
    /**
     * Enable or disable auto-start functionality
     */
    @ReactMethod
    public void setAutoStartEnabled(boolean enabled, Promise promise) {
        try {
            BootReceiver.setAutoStartEnabled(reactContext, enabled);
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putBoolean("autoStartEnabled", enabled);
            result.putString("message", "Auto-start " + (enabled ? "enabled" : "disabled"));
            
            promise.resolve(result);
            Log.d(TAG, "🔧 Auto-start " + (enabled ? "enabled" : "disabled"));
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to set auto-start", e);
            promise.reject("SET_AUTOSTART_ERROR", "Failed to set auto-start: " + e.getMessage());
        }
    }
    
    /**
     * Check if auto-start is enabled
     */
    @ReactMethod
    public void isAutoStartEnabled(Promise promise) {
        try {
            boolean enabled = BootReceiver.isAutoStartEnabled(reactContext);
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("autoStartEnabled", enabled);
            
            promise.resolve(result);
            Log.d(TAG, "📊 Auto-start status: " + (enabled ? "enabled" : "disabled"));
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to check auto-start status", e);
            promise.reject("CHECK_AUTOSTART_ERROR", "Failed to check auto-start status: " + e.getMessage());
        }
    }
    
    /**
     * Request battery optimization exemption for better background performance
     */
    @ReactMethod
    public void requestBatteryOptimizationExemption(Promise promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + reactContext.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                
                reactContext.startActivity(intent);
                
                WritableMap result = Arguments.createMap();
                result.putBoolean("success", true);
                result.putString("message", "Battery optimization exemption requested");
                
                promise.resolve(result);
                Log.d(TAG, "🔋 Battery optimization exemption requested");
                
            } else {
                WritableMap result = Arguments.createMap();
                result.putBoolean("success", false);
                result.putString("message", "Battery optimization not available on this Android version");
                
                promise.resolve(result);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to request battery optimization exemption", e);
            promise.reject("BATTERY_OPTIMIZATION_ERROR", "Failed to request battery optimization exemption: " + e.getMessage());
        }
    }

    // Overlay permission methods removed - using React Native overlay only
    
    /**
     * Get comprehensive background service status
     */
    @ReactMethod
    public void getServiceStatus(Promise promise) {
        try {
            WritableMap status = Arguments.createMap();
            
            // Service running status
            status.putBoolean("isServiceRunning", isCallDetectionServiceRunning());
            
            // Auto-start status
            status.putBoolean("autoStartEnabled", BootReceiver.isAutoStartEnabled(reactContext));
            
            // Battery optimization status
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                android.os.PowerManager pm = (android.os.PowerManager) reactContext.getSystemService(Context.POWER_SERVICE);
                status.putBoolean("batteryOptimizationExempted", pm.isIgnoringBatteryOptimizations(reactContext.getPackageName()));
            } else {
                status.putBoolean("batteryOptimizationExempted", true);
            }
            
            // Overlay permission status
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                status.putBoolean("overlayPermissionGranted", Settings.canDrawOverlays(reactContext));
            } else {
                status.putBoolean("overlayPermissionGranted", true);
            }
            
            promise.resolve(status);
            Log.d(TAG, "📊 Comprehensive service status retrieved");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to get service status", e);
            promise.reject("GET_STATUS_ERROR", "Failed to get service status: " + e.getMessage());
        }
    }
    
    /**
     * Check if the CallDetectionService is currently running
     */
    private boolean isCallDetectionServiceRunning() {
        ActivityManager manager = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo> services = manager.getRunningServices(Integer.MAX_VALUE);
        
        for (ActivityManager.RunningServiceInfo service : services) {
            if (CallDetectionService.class.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }
}
