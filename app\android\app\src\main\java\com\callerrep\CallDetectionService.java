package com.callerrep;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.os.Build;
import android.os.IBinder;
import android.provider.ContactsContract;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.util.Log;
import androidx.core.app.NotificationCompat;

import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.modules.core.DeviceEventManagerModule;

/**
 * Background service for detecting incoming calls even when app is closed
 */
public class CallDetectionService extends Service {
    private static final String TAG = "CallDetectionService";
    private static final String CHANNEL_ID = "CallerRepCallDetection";
    private static final int NOTIFICATION_ID = 1001;
    
    private TelephonyManager telephonyManager;
    private PhoneStateListener phoneStateListener;
    private ContactSyncManager contactSyncManager;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "📞 CallDetectionService onCreate() called");

        try {
            // Create notification channel first
            createNotificationChannel();
            Log.d(TAG, "📞 Notification channel created");

            // Create and start foreground notification
            Notification notification = createNotification();
            startForeground(NOTIFICATION_ID, notification);
            Log.d(TAG, "📞 Foreground service started with notification");

            // Setup phone state listener
            setupPhoneStateListener();
            Log.d(TAG, "📞 Phone state listener setup complete");

            // Native overlay manager removed - using React Native overlay only
            Log.d(TAG, "📞 Using React Native overlay only");

            // Initialize contact sync manager
            contactSyncManager = new ContactSyncManager(this);
            Log.d(TAG, "📇 Contact sync manager initialized");

            // Perform initial contact sync if needed
            contactSyncManager.performSyncIfNeeded();
            Log.d(TAG, "📇 Initial contact sync check completed");

            // For testing: force a sync on first run (remove this in production)
            contactSyncManager.forceSyncNow();
            Log.d(TAG, "📇 Force sync triggered for testing");

            Log.d(TAG, "✅ CallDetectionService created successfully");

        } catch (Exception e) {
            Log.e(TAG, "❌ Error in CallDetectionService onCreate", e);
            // Try to continue with minimal functionality
            try {
                Notification minimalNotification = new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("CallerRep")
                    .setContentText("Service starting...")
                    .setSmallIcon(android.R.drawable.ic_menu_call)
                    .build();
                startForeground(NOTIFICATION_ID, minimalNotification);
                Log.d(TAG, "📞 Started with minimal notification");
            } catch (Exception e2) {
                Log.e(TAG, "❌ Failed to start even minimal service", e2);
                stopSelf(); // Stop service if we can't even create a notification
            }
        }
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "📞 CallDetectionService started");
        
        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null; // Not a bound service
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "📞 CallDetectionService destroyed");
        
        if (telephonyManager != null && phoneStateListener != null) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
        }
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "CallerRep Call Detection",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Background service for detecting incoming calls and providing quality information");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }
    
    private Notification createNotification() {
        try {
            Intent notificationIntent = new Intent(this, MainActivity.class);

            // Use FLAG_IMMUTABLE for Android 12+ compatibility
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, notificationIntent, flags
            );

            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("CallerRep Active")
                .setContentText("Monitoring incoming calls for quality information")
                .setSmallIcon(android.R.drawable.ic_menu_call) // Use system icon as fallback
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setAutoCancel(false)
                .setShowWhen(false)
                .build();

        } catch (Exception e) {
            Log.e(TAG, "❌ Error creating notification, using minimal notification", e);

            // Fallback minimal notification
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("CallerRep")
                .setContentText("Background service running")
                .setSmallIcon(android.R.drawable.ic_menu_call)
                .setOngoing(true)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
        }
    }
    
    private void setupPhoneStateListener() {
        try {
            Log.d(TAG, "📞 Setting up phone state listener...");

            telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager == null) {
                Log.e(TAG, "❌ TelephonyManager not available");
                return;
            }

            phoneStateListener = new PhoneStateListener() {
                @Override
                public void onCallStateChanged(int state, String phoneNumber) {
                    try {
                        super.onCallStateChanged(state, phoneNumber);

                        // Sanitize phone number for logging
                        String logNumber = (phoneNumber != null && !phoneNumber.isEmpty()) ?
                            phoneNumber.replaceAll("\\d", "*") : "unknown";

                        switch (state) {
                            case TelephonyManager.CALL_STATE_RINGING:
                                Log.d(TAG, "📞 Incoming call detected: " + logNumber);
                                handleIncomingCall(phoneNumber);
                                break;

                            case TelephonyManager.CALL_STATE_OFFHOOK:
                                Log.d(TAG, "📞 Call answered: " + logNumber);
                                handleCallAnswered(phoneNumber);
                                break;

                            case TelephonyManager.CALL_STATE_IDLE:
                                Log.d(TAG, "📞 Call ended");
                                handleCallEnded();
                                break;

                            default:
                                Log.d(TAG, "📞 Unknown call state: " + state);
                                break;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "❌ Error in onCallStateChanged", e);
                    }
                }
            };

            // Register the listener
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
            Log.d(TAG, "✅ Phone state listener registered successfully");

        } catch (SecurityException e) {
            Log.e(TAG, "❌ Permission denied for phone state listener", e);
        } catch (Exception e) {
            Log.e(TAG, "❌ Error setting up phone state listener", e);
        }
    }
    
    private void handleIncomingCall(String phoneNumber) {
        try {
            // Send event to React Native if app is running
            sendEventToReactNative("onIncomingCall", phoneNumber);

            // Check if the number is already in contacts
            if (isNumberInContacts(phoneNumber)) {
                Log.d(TAG, "📞 Number is in contacts - no overlay needed");
                // Update notification but don't show overlay
                updateNotificationWithCallInfo(phoneNumber, true);
            } else {
                Log.d(TAG, "📞 Number not in contacts - sending overlay event to React Native");
                // Send event to React Native to show overlay (native overlay removed)
                sendEventToReactNative("onShowCallQualityOverlay", phoneNumber);
                // Update notification with call info
                updateNotificationWithCallInfo(phoneNumber, false);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error handling incoming call", e);
        }
    }
    
    private void handleCallAnswered(String phoneNumber) {
        try {
            sendEventToReactNative("onCallAnswered", phoneNumber);
            Log.d(TAG, "📞 Call answered event sent");
        } catch (Exception e) {
            Log.e(TAG, "Error handling call answered", e);
        }
    }
    
    private void handleCallEnded() {
        try {
            sendEventToReactNative("onCallEnded", null);

            // Send event to dismiss call quality overlay (native overlay removed)
            sendEventToReactNative("onDismissCallQualityOverlay", null);

            // Reset notification
            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            manager.notify(NOTIFICATION_ID, createNotification());

            Log.d(TAG, "📞 Call ended event sent");
        } catch (Exception e) {
            Log.e(TAG, "Error handling call ended", e);
        }
    }
    
    private void sendEventToReactNative(String eventName, String phoneNumber) {
        try {
            Log.d(TAG, "📞 Attempting to send event to React Native: " + eventName);

            // Safely get React context from MainApplication
            MainApplication app = (MainApplication) getApplication();
            if (app == null) {
                Log.w(TAG, "📞 MainApplication not available, event not sent: " + eventName);
                return;
            }

            // Check if React Native host is available
            if (app.getReactNativeHost() == null) {
                Log.w(TAG, "📞 React Native host not available, event not sent: " + eventName);
                return;
            }

            // Check if React Instance Manager is available
            if (app.getReactNativeHost().getReactInstanceManager() == null) {
                Log.w(TAG, "📞 React Instance Manager not available, event not sent: " + eventName);
                return;
            }

            ReactContext reactContext = app.getReactNativeHost()
                .getReactInstanceManager()
                .getCurrentReactContext();

            if (reactContext != null && reactContext.hasActiveCatalystInstance()) {
                WritableMap params = Arguments.createMap();
                if (phoneNumber != null && !phoneNumber.isEmpty()) {
                    params.putString("phoneNumber", phoneNumber);
                }
                params.putDouble("timestamp", System.currentTimeMillis());
                params.putString("source", "background_service");

                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit(eventName, params);

                Log.d(TAG, "📞 Event sent to React Native successfully: " + eventName);
            } else {
                Log.w(TAG, "📞 React Native context not available or destroyed, event not sent: " + eventName);
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ Error sending event to React Native: " + eventName, e);
            // Don't rethrow - service should continue working even if React Native communication fails
        }
    }
    
    // Native overlay method removed - using React Native overlay only
    
    private void updateNotificationWithCallInfo(String phoneNumber, boolean isInContacts) {
        try {
            String contentText = isInContacts ?
                "Known contact calling" :
                "Analyzing quality for: " + phoneNumber;

            Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Incoming Call")
                .setContentText(contentText)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setOngoing(true)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_CALL)
                .build();

            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            manager.notify(NOTIFICATION_ID, notification);

        } catch (Exception e) {
            Log.e(TAG, "Error updating notification", e);
        }
    }

    /**
     * Check if a phone number is already in the device's contacts
     */
    private boolean isNumberInContacts(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }

        try {
            // Normalize the phone number for comparison (remove spaces, dashes, etc.)
            String normalizedNumber = normalizePhoneNumber(phoneNumber);

            // Query the contacts database for phone numbers
            Cursor cursor = getContentResolver().query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                new String[]{ContactsContract.CommonDataKinds.Phone.NUMBER},
                null, null, null
            );

            if (cursor != null) {
                try {
                    while (cursor.moveToNext()) {
                        String contactNumber = cursor.getString(0);
                        if (contactNumber != null) {
                            String normalizedContactNumber = normalizePhoneNumber(contactNumber);
                            if (normalizedNumber.equals(normalizedContactNumber)) {
                                Log.d(TAG, "📞 Found matching contact for number");
                                return true;
                            }
                        }
                    }
                } finally {
                    cursor.close();
                }
            }

            Log.d(TAG, "📞 No matching contact found for number");
            return false;

        } catch (SecurityException e) {
            Log.w(TAG, "📞 No permission to read contacts - showing overlay by default", e);
            return false; // If we can't read contacts, show overlay to be safe
        } catch (Exception e) {
            Log.e(TAG, "📞 Error checking contacts", e);
            return false; // If error occurs, show overlay to be safe
        }
    }

    /**
     * Normalize phone number for comparison by removing non-digit characters
     * and handling international prefixes
     */
    private String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }

        // Remove all non-digit characters
        String digitsOnly = phoneNumber.replaceAll("[^\\d]", "");

        // Handle common international prefixes
        if (digitsOnly.startsWith("1") && digitsOnly.length() == 11) {
            // Remove US country code if present
            digitsOnly = digitsOnly.substring(1);
        } else if (digitsOnly.startsWith("0") && digitsOnly.length() > 10) {
            // Remove leading zero for some international formats
            digitsOnly = digitsOnly.substring(1);
        }

        return digitsOnly;
    }

    /**
     * Static method to start the service from other components
     */
    public static void startService(Context context) {
        Intent serviceIntent = new Intent(context, CallDetectionService.class);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }
        
        Log.d(TAG, "📞 CallDetectionService start requested");
    }
    
    /**
     * Static method to stop the service
     */
    public static void stopService(Context context) {
        Intent serviceIntent = new Intent(context, CallDetectionService.class);
        context.stopService(serviceIntent);
        Log.d(TAG, "📞 CallDetectionService stop requested");
    }
}
